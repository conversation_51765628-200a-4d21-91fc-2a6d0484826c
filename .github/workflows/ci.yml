name: Python CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Lint with flake8
        working-directory: habit-tracker
        run: |
          echo "Running flake8..."
          flake8 app tests
          echo "Linting completed."

      - name: Run unit tests
        working-directory: habit-tracker
        env:
            PYTHONPATH: .
        run: |
          echo "Running pytest..."
          pytest tests
          echo "Unit tests completed."