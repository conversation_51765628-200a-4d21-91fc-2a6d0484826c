name: Docker CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    uses: ./.github/workflows/test.yml

  build:
    needs: test
    if: github.event_name == 'pull_request'  # Triggered on merge requests
    uses: ./.github/workflows/build.yml

  deploy-staging:
    needs: build
    if: github.event_name == 'pull_request' && github.base_ref == 'develop'  # MR to develop branch
    uses: ./.github/workflows/deploy-staging.yml
    with:
      image: ${{ needs.build.outputs.image }}
    secrets: inherit

  deploy-production:
    needs: build
    if: github.event_name == 'pull_request' && github.base_ref == 'main'  # MR to main branch
    uses: ./.github/workflows/deploy-production.yml
    with:
      image: ${{ needs.build.outputs.image }}
    secrets: inherit
