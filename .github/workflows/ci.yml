name: Docker CI/CD Pipeline

on:
  pull_request:
    branches: [main]

permissions:
  contents: read
  packages: write

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    uses: ./.github/workflows/test.yml

  build:
    needs: test
    uses: ./.github/workflows/build.yml

  deploy:
    needs: build
    if: github.event_name == 'pull_request' && github.base_ref == 'main'
    uses: ./.github/workflows/deploy.yml
    with:
      image: ${{ needs.build.outputs.image }}
    secrets: inherit
