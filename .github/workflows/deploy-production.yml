name: Deploy to Production

on:
  workflow_call:
    inputs:
      image:
        required: true
        type: string
    secrets:
      DEPLOY_VM_IP:
        required: true
      DEPLOY_SSH_KEY:
        required: true
      DB_HOST:
        required: true
      DB_PORT:
        required: true
      DB_NAME:
        required: true
      DB_USER:
        required: true
      DB_PASSWORD:
        required: true
      SECRET_KEY:
        required: true
      FLASK_ENV:
        required: true

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: production
    env:
      VM_IP: ${{ secrets.DEPLOY_VM_IP }}
      SSH_PRIVATE_KEY: ${{ secrets.DEPLOY_SSH_KEY }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key
        run: |
          echo "🔍 Production VM IP: $VM_IP"
          echo "🔍 SSH key length: $(echo "$SSH_PRIVATE_KEY" | wc -c) characters"

          if [ -z "$VM_IP" ]; then
            echo "❌ VM IP is empty. Check DEPLOY_VM_IP secret."
            exit 1
          fi

          if [ -z "$SSH_PRIVATE_KEY" ]; then
            echo "❌ SSH private key is empty. Check DEPLOY_SSH_KEY secret."
            exit 1
          fi

          echo "✅ Production VM IP: $VM_IP"
          echo "✅ SSH key length: $(echo "$SSH_PRIVATE_KEY" | wc -c) characters"

          # Setup SSH directory and key
          mkdir -p ~/.ssh

          # Write SSH key with proper formatting
          echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_rsa

          # Ensure proper permissions
          chmod 600 ~/.ssh/id_rsa
          chmod 700 ~/.ssh

          # Verify key format
          echo "🔍 Verifying SSH key format..."
          if ssh-keygen -l -f ~/.ssh/id_rsa 2>/dev/null; then
            echo "✅ SSH key format is valid"
          else
            echo "❌ SSH key format is invalid"
            exit 1
          fi

          # Create SSH config for better connection handling
          cat > ~/.ssh/config << EOF
          Host production-vm
            HostName $VM_IP
            User azureuser
            IdentityFile ~/.ssh/id_rsa
            StrictHostKeyChecking no
            UserKnownHostsFile /dev/null
            ServerAliveInterval 60
            ServerAliveCountMax 3
            ConnectTimeout 30
          EOF
          chmod 600 ~/.ssh/config

          echo "✅ Production VM SSH setup complete"

      - name: Create production environment file
        run: |
          echo "🔍 Creating production environment file..."

          cat > .env << EOF
          CONTAINER_IMAGE=${{ inputs.image }}

          # Flask Configuration
          FLASK_ENV=${{ secrets.FLASK_ENV }}
          SECRET_KEY=${{ secrets.SECRET_KEY }}

          # Database Configuration
          DB_HOST=${{ secrets.DB_HOST }}
          DB_PORT=${{ secrets.DB_PORT }}
          DB_NAME=${{ secrets.DB_NAME }}
          DB_USER=${{ secrets.DB_USER }}
          DB_PASSWORD=${{ secrets.DB_PASSWORD }}

          EOF

          echo "✅ Production environment file created"

      - name: Copy deployment files to production VM
        run: |
          echo "📁 Copying deployment files to production VM..."
          scp -F ~/.ssh/config .env production-vm:/tmp/
          scp -F ~/.ssh/config docker-compose.yml production-vm:/tmp/
          echo "✅ Files copied successfully to production"

      - name: Deploy application to production
        run: |
          echo "🚀 Starting production deployment..."
          ssh -F ~/.ssh/config production-vm << 'EOF'

            # Setup deployment directory
            sudo mkdir -p /opt/habit-tracker
            sudo cp /tmp/.env /opt/habit-tracker/.env
            sudo cp /tmp/docker-compose.yml /opt/habit-tracker/
            cd /opt/habit-tracker

            # Login to GitHub Container Registry
            echo "${{ secrets.GITHUB_TOKEN }}" | sudo docker login ghcr.io -u ${{ github.actor }} --password-stdin

            # Stop existing services
            sudo docker compose down || true

            # Pull latest images
            sudo docker compose pull

            # Start services with environment file
            sudo docker compose --env-file .env up -d

            echo "✅ Production application deployed successfully!"

            # Show running containers
            echo "📋 Running containers:"
            sudo docker compose ps
          EOF

      - name: Verify production deployment
        run: |
          echo "🔍 Verifying production deployment..."
          sleep 30  # Wait for services to start

          # Test if the application is responding
          if curl -f -s --connect-timeout 10 http://${{ secrets.DEPLOY_VM_IP }} > /dev/null; then
            echo "✅ Production application is responding"
          else
            echo "⚠️ Production application may still be starting up"
          fi

      - name: Cleanup
        if: always()
        run: |
          rm -f ~/.ssh/id_rsa
          rm -f ~/.ssh/config
          rm -f .env
