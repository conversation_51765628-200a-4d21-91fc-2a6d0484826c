# Terraform Infrastructure for Habit Tracker

This directory contains Terraform configurations for deploying the Habit Tracker application infrastructure on Azure.

## Architecture

The infrastructure includes:
- **Resource Group**: Environment-specific resource group
- **Virtual Network**: VNet with web subnet
- **Network Security Group**: Firewall rules for SSH and HTTP access
- **Virtual Machine**: Ubuntu 22.04 LTS with <PERSON><PERSON> and <PERSON><PERSON>x
- **Public IP**: Static IP address for external access
- **SSH Key Pair**: Auto-generated SSH keys for secure access

## Environments

### Available Environments
- **prod**: Production environment (`environments/prod.tfvars`)
- **staging**: Staging environment (`environments/staging.tfvars`)
- **dev**: Development environment (create `environments/dev.tfvars` if needed)

### Environment Isolation
Each environment creates completely separate resources:
- `habit-tracker-prod-rg` (Production Resource Group)
- `habit-tracker-staging-rg` (Staging Resource Group)
- Separate VMs, networks, and state files

## Quick Start

### Prerequisites
1. Azure CLI installed and authenticated
2. Terraform >= 1.0 installed
3. Appropriate Azure permissions

### Deploy Staging Environment

```bash
# Navigate to terraform directory
cd terraform

# Plan the staging deployment
./deploy.sh staging plan

# Apply the staging deployment
./deploy.sh staging apply
```

### Deploy Production Environment

```bash
# Plan the production deployment
./deploy.sh prod plan

# Apply the production deployment
./deploy.sh prod apply
```

## Deployment Script Usage

The `deploy.sh` script simplifies environment management:

```bash
# Syntax
./deploy.sh <environment> [action]

# Examples
./deploy.sh staging plan     # Plan staging changes
./deploy.sh staging apply    # Apply staging changes
./deploy.sh prod plan        # Plan production changes
./deploy.sh prod apply       # Apply production changes
./deploy.sh staging destroy  # Destroy staging environment
```

## Manual Terraform Commands

If you prefer manual Terraform commands:

```bash
# Initialize with environment-specific state
terraform init -backend-config="key=habit-tracker-staging.terraform.tfstate" -reconfigure

# Plan with environment variables
terraform plan -var-file="environments/staging.tfvars"

# Apply with environment variables
terraform apply -var-file="environments/staging.tfvars"
```

## Environment Configuration

### Staging Configuration (`environments/staging.tfvars`)
```hcl
project_name = "habit-tracker"
environment  = "staging"
location     = "South Africa North"
owner        = "DevOps Team"
vm_size      = "Standard_B2s"
admin_username = "azureuser"
```

### Production Configuration (`environments/prod.tfvars`)
```hcl
project_name = "habit-tracker"
environment  = "prod"
location     = "South Africa North"
owner        = "DevOps Team"
vm_size      = "Standard_B2s"
admin_username = "azureuser"
```

## Outputs

After deployment, Terraform provides these outputs:
- `public_ip_address`: VM's public IP address
- `ssh_connection_command`: SSH command to connect to the VM
- `application_url_http`: HTTP URL for the application
- `vm_name`: Name of the created VM
- `resource_group_name`: Name of the resource group

## VM Configuration

Each VM is configured with:
- **OS**: Ubuntu 22.04 LTS
- **Docker**: Latest Docker CE with Docker Compose
- **Nginx**: Reverse proxy configuration
- **Firewall**: UFW with SSH and HTTP access
- **Users**: `azureuser` (admin) and `appuser` (application)

### Nginx Configuration
- HTTP traffic on port 80 → Application on localhost:5000
- `/db` path → pgAdmin on localhost:8080

## State Management

Each environment uses a separate Terraform state file:
- Production: `habit-tracker-prod.terraform.tfstate`
- Staging: `habit-tracker-staging.terraform.tfstate`

State files are stored in Azure Storage:
- Resource Group: `terraform-state-rg`
- Storage Account: `tfstate5190e1d3`
- Container: `tfstate`

## Scaling and Customization

### VM Sizing
Modify `vm_size` in the environment's `.tfvars` file:
```hcl
# For more resources
vm_size = "Standard_B4ms"  # 4 vCPUs, 16 GB RAM

# For cost optimization
vm_size = "Standard_B1s"   # 1 vCPU, 1 GB RAM
```

### Location Changes
Update the `location` variable:
```hcl
location = "East US"  # or any other Azure region
```

## Troubleshooting

### Common Issues

1. **State file conflicts**: Ensure you're using the correct state key for each environment
2. **Azure permissions**: Verify your service principal has Contributor access
3. **Resource quotas**: Check Azure subscription limits for VM sizes
4. **SSH access**: Use the generated private key from Terraform outputs

### Getting SSH Access

```bash
# Get the SSH private key (sensitive output)
terraform output -raw ssh_private_key > private_key.pem
chmod 600 private_key.pem

# Connect to the VM
ssh -i private_key.pem azureuser@<VM_PUBLIC_IP>
```

## Security Considerations

- SSH keys are auto-generated per environment
- Network Security Groups restrict access to SSH (22) and HTTP (80)
- UFW firewall is enabled on each VM
- VMs are in separate resource groups per environment

## Cost Management

- Use `Standard_B1s` for development/testing
- Use `Standard_B2s` or higher for production workloads
- Consider Azure Reserved Instances for long-term deployments
- Enable auto-shutdown for non-production environments
