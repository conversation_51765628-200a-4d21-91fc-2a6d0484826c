#!/bin/bash

# Terraform deployment script for different environments
# Usage: ./deploy.sh <environment> [plan|apply|destroy]
# Example: ./deploy.sh staging plan
#          ./deploy.sh prod apply

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if environment is provided
if [ $# -lt 1 ]; then
    print_error "Usage: $0 <environment> [plan|apply|destroy]"
    print_info "Available environments: dev, staging, prod"
    exit 1
fi

ENVIRONMENT=$1
ACTION=${2:-plan}

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(dev|staging|prod)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_info "Available environments: dev, staging, prod"
    exit 1
fi

# Validate action
if [[ ! "$ACTION" =~ ^(plan|apply|destroy)$ ]]; then
    print_error "Invalid action: $ACTION"
    print_info "Available actions: plan, apply, destroy"
    exit 1
fi

# Set variables
TFVARS_FILE="environments/${ENVIRONMENT}.tfvars"
STATE_KEY="habit-tracker-${ENVIRONMENT}.terraform.tfstate"

print_info "Deploying to environment: $ENVIRONMENT"
print_info "Using tfvars file: $TFVARS_FILE"
print_info "Using state key: $STATE_KEY"

# Check if tfvars file exists
if [ ! -f "$TFVARS_FILE" ]; then
    print_error "Environment file not found: $TFVARS_FILE"
    exit 1
fi

# Initialize Terraform with environment-specific backend
print_info "Initializing Terraform..."
terraform init -backend-config="key=$STATE_KEY" -reconfigure

# Run the specified action
case $ACTION in
    plan)
        print_info "Running Terraform plan..."
        terraform plan -var-file="$TFVARS_FILE" -out="terraform-${ENVIRONMENT}.tfplan"
        print_success "Plan completed successfully!"
        print_info "To apply this plan, run: ./deploy.sh $ENVIRONMENT apply"
        ;;
    apply)
        if [ -f "terraform-${ENVIRONMENT}.tfplan" ]; then
            print_info "Applying Terraform plan..."
            terraform apply "terraform-${ENVIRONMENT}.tfplan"
            rm -f "terraform-${ENVIRONMENT}.tfplan"
        else
            print_warning "No plan file found. Running plan and apply..."
            terraform plan -var-file="$TFVARS_FILE" -out="terraform-${ENVIRONMENT}.tfplan"
            terraform apply "terraform-${ENVIRONMENT}.tfplan"
            rm -f "terraform-${ENVIRONMENT}.tfplan"
        fi
        print_success "Infrastructure deployed successfully!"
        
        # Show outputs
        print_info "Infrastructure outputs:"
        terraform output
        ;;
    destroy)
        print_warning "This will destroy all resources in the $ENVIRONMENT environment!"
        read -p "Are you sure you want to continue? (yes/no): " confirm
        if [ "$confirm" = "yes" ]; then
            print_info "Destroying infrastructure..."
            terraform destroy -var-file="$TFVARS_FILE" -auto-approve
            print_success "Infrastructure destroyed successfully!"
        else
            print_info "Destroy cancelled."
        fi
        ;;
esac
