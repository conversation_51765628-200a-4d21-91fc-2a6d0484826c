#!/bin/bash

# Terraform deployment script for habit-tracker environments
# Usage: ./deploy.sh <environment> <action>
# Example: ./deploy.sh staging plan
# Example: ./deploy.sh prod apply

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required arguments are provided
if [ $# -lt 2 ]; then
    print_error "Usage: $0 <environment> <action>"
    print_error "Environment: staging, prod"
    print_error "Action: init, plan, apply, destroy, output"
    exit 1
fi

ENVIRONMENT=$1
ACTION=$2

# Validate environment
if [[ ! "$ENVIRONMENT" =~ ^(staging|prod)$ ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: staging, prod"
    exit 1
fi

# Validate action
if [[ ! "$ACTION" =~ ^(init|plan|apply|destroy|output|show)$ ]]; then
    print_error "Invalid action: $ACTION"
    print_error "Valid actions: init, plan, apply, destroy, output, show"
    exit 1
fi

# Set environment-specific variables
TFVARS_FILE="environments/${ENVIRONMENT}.tfvars"
STATE_KEY="habit-tracker-${ENVIRONMENT}.tfstate"

# Check if tfvars file exists
if [ ! -f "$TFVARS_FILE" ]; then
    print_error "Environment file not found: $TFVARS_FILE"
    exit 1
fi

print_status "Starting Terraform $ACTION for $ENVIRONMENT environment"
print_status "Using tfvars file: $TFVARS_FILE"
print_status "Using state key: $STATE_KEY"

# Initialize Terraform with environment-specific backend
if [ "$ACTION" = "init" ] || [ ! -d ".terraform" ]; then
    print_status "Initializing Terraform..."
    terraform init \
        -backend-config="key=$STATE_KEY" \
        -reconfigure
    print_success "Terraform initialized successfully"
fi

# Execute the requested action
case $ACTION in
    "init")
        print_success "Terraform initialization completed"
        ;;
    "plan")
        print_status "Running Terraform plan..."
        terraform plan -var-file="$TFVARS_FILE" -out="${ENVIRONMENT}.tfplan"
        print_success "Terraform plan completed. Plan saved as ${ENVIRONMENT}.tfplan"
        ;;
    "apply")
        print_status "Running Terraform apply..."
        if [ -f "${ENVIRONMENT}.tfplan" ]; then
            terraform apply "${ENVIRONMENT}.tfplan"
            rm -f "${ENVIRONMENT}.tfplan"
        else
            print_warning "No plan file found. Running apply with auto-approve..."
            terraform apply -var-file="$TFVARS_FILE" -auto-approve
        fi
        print_success "Terraform apply completed successfully"
        
        # Show outputs after successful apply
        print_status "Displaying outputs..."
        terraform output
        ;;
    "destroy")
        print_warning "This will destroy all resources in the $ENVIRONMENT environment!"
        read -p "Are you sure you want to continue? (yes/no): " confirm
        if [ "$confirm" = "yes" ]; then
            terraform destroy -var-file="$TFVARS_FILE" -auto-approve
            print_success "Resources destroyed successfully"
        else
            print_status "Destroy operation cancelled"
        fi
        ;;
    "output")
        print_status "Displaying Terraform outputs..."
        terraform output
        ;;
    "show")
        print_status "Displaying current Terraform state..."
        terraform show
        ;;
esac

print_success "Terraform $ACTION operation completed for $ENVIRONMENT environment"
