# CI/CD Pipeline Setup

This document describes the updated CI/CD pipeline that supports both staging and production deployments.

## Overview

The pipeline now supports two environments:
- **Staging**: Deploys when code is pushed to the `develop` branch
- **Production**: Deploys when code is pushed to the `main` branch

## Workflow Structure

### Main CI Pipeline (`.github/workflows/ci.yml`)
- Triggers on push to `main` or `develop` branches
- Triggers on pull requests to `main` or `develop` branches
- Runs tests, builds Docker image, and deploys to appropriate environment

### Staging Deployment (`.github/workflows/deploy-staging.yml`)
- Deploys to staging VM when code is pushed to `develop` branch
- Uses staging-specific secrets and configuration
- Deploys to `/opt/habit-tracker-staging` directory

### Production Deployment (`.github/workflows/deploy-production.yml`)
- Deploys to production VM when code is pushed to `main` branch
- Uses production secrets and configuration
- Deploys to `/opt/habit-tracker` directory

## Environment Configuration

### Staging Environment
- **VM**: `habit-tracker-staging-vm` (************)
- **Resource Group**: `habit-tracker-staging-rg`
- **Database**: `habit_tracker_staging`
- **Flask Environment**: `staging`
- **Deployment Path**: `/opt/habit-tracker-staging`

### Production Environment
- **VM**: `habit-tracker-prod-vm`
- **Resource Group**: `habit-tracker-prod-rg`
- **Database**: Production database
- **Flask Environment**: `production`
- **Deployment Path**: `/opt/habit-tracker`

## GitHub Secrets

### Production Secrets
- `DEPLOY_VM_IP`: Production VM IP address
- `DEPLOY_SSH_KEY`: SSH private key for production VM
- `DB_HOST`: Production database host
- `DB_PORT`: Production database port
- `DB_NAME`: Production database name
- `DB_USER`: Production database user
- `DB_PASSWORD`: Production database password
- `SECRET_KEY`: Production Flask secret key
- `FLASK_ENV`: Production Flask environment

### Staging Secrets
- `STAGING_VM_IP`: Staging VM IP address (************)
- `STAGING_SSH_KEY`: SSH private key for staging VM
- `STAGING_DB_HOST`: Staging database host (localhost)
- `STAGING_DB_PORT`: Staging database port (5432)
- `STAGING_DB_NAME`: Staging database name (habit_tracker_staging)
- `STAGING_DB_USER`: Staging database user (postgres)
- `STAGING_DB_PASSWORD`: Staging database password
- `STAGING_SECRET_KEY`: Staging Flask secret key
- `STAGING_FLASK_ENV`: Staging Flask environment (staging)

## Deployment Flow

### Staging Deployment
1. Developer pushes code to `develop` branch
2. CI pipeline runs tests and builds Docker image
3. Staging deployment workflow is triggered
4. Application is deployed to staging VM
5. Verification checks are performed

### Production Deployment
1. Developer merges `develop` branch to `main` branch
2. CI pipeline runs tests and builds Docker image
3. Production deployment workflow is triggered
4. Application is deployed to production VM
5. Verification checks are performed

## Branch Strategy

```
main (production)
├── develop (staging)
    ├── feature/new-feature
    └── bugfix/fix-issue
```

### Recommended Workflow
1. Create feature branches from `develop`
2. Submit pull requests to `develop` for staging deployment
3. Test thoroughly in staging environment
4. Merge `develop` to `main` for production deployment

## Deployment Verification

Both staging and production deployments include:
- SSH connection verification
- Docker image pull and deployment
- Service health checks
- HTTP endpoint verification
- Container status reporting

## Troubleshooting

### Common Issues
1. **SSH Connection Failed**: Check VM IP and SSH key secrets
2. **Docker Login Failed**: Verify GITHUB_TOKEN permissions
3. **Service Not Starting**: Check environment variables and Docker Compose configuration
4. **HTTP Verification Failed**: Application may still be starting up (normal)

### Debugging Steps
1. Check GitHub Actions logs for detailed error messages
2. SSH into the VM manually to check service status
3. Review Docker container logs: `sudo docker compose logs`
4. Verify environment file: `cat /opt/habit-tracker/.env` (or staging path)

## Security Considerations

- SSH keys are environment-specific and stored as GitHub secrets
- Database passwords are different between staging and production
- Flask secret keys are unique per environment
- All secrets are encrypted and only accessible during deployment

## Monitoring

After deployment, monitor:
- Application HTTP response (port 80)
- Database connectivity (port 5432)
- Docker container health
- System resources (CPU, memory, disk)

## Next Steps

1. Test the staging deployment by pushing to `develop` branch
2. Verify staging environment functionality
3. Test production deployment by merging to `main` branch
4. Set up monitoring and alerting for both environments
