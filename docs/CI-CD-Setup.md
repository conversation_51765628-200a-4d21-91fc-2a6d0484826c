# CI/CD Pipeline Setup

This document describes the updated CI/CD pipeline that supports both staging and production deployments with proper separation of concerns.

## Pipeline Strategy

### Merge-Based Deployment
- **Tests always run** on every push and pull request (fast feedback)
- **Build and deployment only happen on merges** (when code is pushed to main/develop)
- This ensures code quality while only deploying when changes are actually merged

## Workflow Structure

### Main CI Pipeline (`.github/workflows/ci.yml`)
```yaml
Triggers:
- Push to any branch: Only runs tests
- Push to main/develop: Runs tests, builds, and deploys

Jobs:
1. test: Always runs (linting, unit tests)
2. build: Only on push to main/develop (Docker image build)
3. deploy-staging: Only on push to develop branch
4. deploy-production: Only on push to main branch
```

### Deployment Logic
- **Staging Deployment**: Triggered when code is merged to `develop` branch
- **Production Deployment**: Triggered when code is merged to `main` branch

### Staging Deployment (`.github/workflows/deploy-staging.yml`)
- Deploys to staging VM when code is merged to `develop` branch
- Uses staging-specific secrets and configuration
- Deploys to `/opt/habit-tracker-staging` directory

### Production Deployment (`.github/workflows/deploy-production.yml`)
- Deploys to production VM when code is merged to `main` branch
- Uses production secrets and configuration
- Deploys to `/opt/habit-tracker` directory

## Environment Configuration

### Staging Environment
- **VM**: `habit-tracker-staging-vm` (************)
- **Resource Group**: `habit-tracker-staging-rg`
- **Database**: `habit_tracker_staging`
- **Flask Environment**: `staging`
- **Deployment Path**: `/opt/habit-tracker-staging`

### Production Environment
- **VM**: Production VM (configured in secrets)
- **Resource Group**: Production resource group
- **Database**: Production database
- **Flask Environment**: `production`
- **Deployment Path**: `/opt/habit-tracker`

## GitHub Secrets

### Production Secrets
- `DEPLOY_VM_IP`: Production VM IP address
- `DEPLOY_SSH_KEY`: SSH private key for production VM
- `DB_HOST`: Production database host (localhost)
- `DB_PORT`: Production database port (5432)
- `DB_NAME`: Production database name
- `DB_USER`: Production database user (postgres)
- `DB_PASSWORD`: Production database password
- `SECRET_KEY`: Production Flask secret key
- `FLASK_ENV`: Production Flask environment

### Staging Secrets
- `STAGING_VM_IP`: Staging VM IP (************)
- `STAGING_SSH_KEY`: SSH private key for staging VM
- `STAGING_DB_HOST`: Staging database host (localhost)
- `STAGING_DB_PORT`: Staging database port (5432)
- `STAGING_DB_NAME`: Staging database name (habit_tracker_staging)
- `STAGING_DB_USER`: Staging database user (postgres)
- `STAGING_DB_PASSWORD`: Staging database password
- `STAGING_SECRET_KEY`: Staging Flask secret key
- `STAGING_FLASK_ENV`: Staging Flask environment (staging)

## Development Workflow

### Recommended Git Flow
```
main (production)
├── develop (staging)
    ├── feature/new-feature
    └── bugfix/fix-issue
```

### Step-by-Step Process

#### 1. Feature Development
```bash
# Create feature branch from develop
git checkout develop
git pull origin develop
git checkout -b feature/new-feature

# Make changes and commit
git add .
git commit -m "Add new feature"
git push origin feature/new-feature
```

#### 2. Staging Deployment
```bash
# Create PR to develop branch and merge it
# This will trigger:
# - Tests (on PR)
# - Build and deploy (on merge to develop)
```

#### 3. Production Deployment
```bash
# After testing in staging, create PR from develop to main and merge it
# This will trigger:
# - Tests (on PR)
# - Build and deploy (on merge to main)
```

## Pipeline Behavior

### On Push to feature branch
- ✅ Tests run (linting, unit tests)
- ❌ Build skipped
- ❌ Deployment skipped

### On Pull Request (any branch)
- ✅ Tests run (linting, unit tests)
- ❌ Build skipped
- ❌ Deployment skipped

### On Merge (Push to main/develop)
- ✅ Tests run (linting, unit tests)
- ✅ Build runs (Docker image creation)
- ✅ Deployment runs (based on target branch)

### Deployment Triggers
- **Merge to develop** → Deploy to staging
- **Merge to main** → Deploy to production

## Terraform Management

### Using the Deploy Script
```bash
# Initialize staging environment
./terraform/deploy.sh staging init

# Plan staging deployment
./terraform/deploy.sh staging plan

# Apply staging changes
./terraform/deploy.sh staging apply

# View staging outputs
./terraform/deploy.sh staging output

# Production commands
./terraform/deploy.sh prod plan
./terraform/deploy.sh prod apply
```

## Benefits of This Approach

1. **Fast Feedback**: Tests run on every push for quick feedback
2. **Resource Efficient**: Builds only happen when code is merged
3. **Environment Isolation**: Staging and production are completely separate
4. **Safe Deployments**: Only deploy when code is actually merged
5. **Code Review**: All deployments require PR approval and merge

## Testing the Pipeline

### Test Staging Deployment
```bash
# Create feature branch
git checkout -b test-staging-pipeline

# Make a small change
echo "# Test change" >> README.md
git add README.md
git commit -m "Test staging pipeline"
git push origin test-staging-pipeline

# Create PR to develop branch and merge it
# This will trigger staging deployment when merged
```

### Test Production Deployment
```bash
# After staging testing is complete
# Create PR from develop to main and merge it
# This will trigger production deployment when merged
```

## Monitoring and Verification

### Deployment Verification Steps
1. SSH connection test
2. Docker image pull and deployment
3. Service health checks
4. HTTP endpoint verification (with 30s wait)
5. Container status reporting

### Manual Verification
```bash
# Check staging deployment
curl http://************

# Check production deployment
curl http://[PRODUCTION_VM_IP]

# SSH into VMs to check logs
ssh azureuser@[VM_IP]
sudo docker compose logs -f
```

## Troubleshooting

### Common Issues
1. **Tests fail on push**: Fix code issues before creating PR
2. **Build fails on PR**: Check Dockerfile and dependencies
3. **Deployment fails**: Verify secrets and VM accessibility
4. **Service not responding**: Check container logs and environment variables

### Debug Commands
```bash
# Check running containers
sudo docker compose ps

# View application logs
sudo docker compose logs app

# View database logs
sudo docker compose logs db

# Check environment file
cat /opt/habit-tracker-staging/.env
```

This pipeline ensures code quality while providing efficient resource usage and safe deployment practices.
